from odoo import models, fields, api

class PatrimoineAjustement(models.Model):
    _name = 'patrimoine.ajustement'
    _description = 'Ajustement Patrimonial'

    name = fields.Char('N° Ajustement', required=True, default='/')
    date_ajustement = fields.Date('Date Ajustement', required=True, default=fields.Date.today)
    
    type_ajustement = fields.Selection([
        ('stock', 'Ajustement Stock'),
        ('detenteur', 'Changement Détenteur'),
        ('valeur', 'Ajustement Valeur')
    ], required=True)
    
    bien_id = fields.Many2one('patrimoine.bien', 'Bien', required=True)
    
    # Ajustement stock
    quantite_theorique = fields.Float('Quantité Théorique')
    quantite_reelle = fields.Float('Quantité Réelle')
    ecart_quantite = fields.Float('Écart', compute='_compute_ecart_quantite')
    
    # Ajustement détenteur
    ancien_detenteur_id = fields.Many2one('patrimoine.detenteur', 'Ancien Détenteur')
    nouveau_detenteur_id = fields.Many2one('patrimoine.detenteur', 'Nouveau Détenteur')
    
    # Ajustement valeur
    ancienne_valeur = fields.Float('Ancienne Valeur')
    nouvelle_valeur = fields.Float('Nouvelle Valeur')
    
    motif = fields.Text('Motif')
    
    state = fields.Selection([
        ('draft', 'Brouillon'),
        ('confirmed', 'Confirmé'),
        ('done', 'Effectué')
    ], default='draft')
    
    @api.depends('quantite_theorique', 'quantite_reelle')
    def _compute_ecart_quantite(self):
        for record in self:
            record.ecart_quantite = record.quantite_reelle - record.quantite_theorique
    
    @api.model
    def create(self, vals):
        if vals.get('name', '/') == '/':
            vals['name'] = self.env['ir.sequence'].next_by_code('patrimoine.ajustement')
        return super().create(vals)
    
    def action_confirm(self):
        self.state = 'confirmed'
    
    def action_done(self):
        self.state = 'done'
        if self.type_ajustement == 'stock':
            self.bien_id.quantite_stock = self.quantite_reelle
        elif self.type_ajustement == 'detenteur':
            self.bien_id.detenteur_actuel_id = self.nouveau_detenteur_id
        elif self.type_ajustement == 'valeur':
            self.bien_id.valeur_acquisition = self.nouvelle_valeur
        
        # Créer un mouvement d'ajustement
        self.env['patrimoine.mouvement'].create({
            'type_mouvement': 'ajustement',
            'bien_id': self.bien_id.id,
            'motif': self.motif,
            'state': 'done'
        })
