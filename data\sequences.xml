<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Séquence pour les biens -->
    <record id="seq_patrimoine_bien" model="ir.sequence">
        <field name="name">Patrimoine Bien</field>
        <field name="code">patrimoine.bien</field>
        <field name="prefix">BIEN/</field>
        <field name="padding">5</field>
        <field name="number_next">1</field>
        <field name="number_increment">1</field>
    </record>

    <!-- Séquence pour les mouvements -->
    <record id="seq_patrimoine_mouvement" model="ir.sequence">
        <field name="name">Patrimoine Mouvement</field>
        <field name="code">patrimoine.mouvement</field>
        <field name="prefix">MOV/</field>
        <field name="padding">5</field>
        <field name="number_next">1</field>
        <field name="number_increment">1</field>
    </record>

    <!-- Séquence pour les procès-verbaux -->
    <record id="seq_patrimoine_proces_verbal" model="ir.sequence">
        <field name="name">Patrimoine Procès-Verbal</field>
        <field name="code">patrimoine.proces.verbal</field>
        <field name="prefix">PV/</field>
        <field name="padding">5</field>
        <field name="number_next">1</field>
        <field name="number_increment">1</field>
    </record>

    <!-- Séquence pour les factures -->
    <record id="seq_patrimoine_facture" model="ir.sequence">
        <field name="name">Patrimoine Facture</field>
        <field name="code">patrimoine.facture</field>
        <field name="prefix">FACT/</field>
        <field name="padding">5</field>
        <field name="number_next">1</field>
        <field name="number_increment">1</field>
    </record>

    <!-- Séquence pour le stock -->
    <record id="seq_patrimoine_stock" model="ir.sequence">
        <field name="name">Patrimoine Stock</field>
        <field name="code">patrimoine.stock</field>
        <field name="prefix">STOCK/</field>
        <field name="padding">5</field>
        <field name="number_next">1</field>
        <field name="number_increment">1</field>
    </record>

    <!-- Séquence pour les ordres d'entrée -->
    <record id="seq_patrimoine_ordre_entree" model="ir.sequence">
        <field name="name">Patrimoine Ordre Entrée</field>
        <field name="code">patrimoine.ordre.entree</field>
        <field name="prefix">OE/</field>
        <field name="padding">5</field>
        <field name="number_next">1</field>
        <field name="number_increment">1</field>
    </record>

    <!-- Séquence pour les bons de sortie -->
    <record id="seq_patrimoine_bon_sortie" model="ir.sequence">
        <field name="name">Patrimoine Bon Sortie</field>
        <field name="code">patrimoine.bon.sortie</field>
        <field name="prefix">BS/</field>
        <field name="padding">5</field>
        <field name="number_next">1</field>
        <field name="number_increment">1</field>
    </record>

    <!-- Séquence pour les ajustements -->
    <record id="seq_patrimoine_ajustement" model="ir.sequence">
        <field name="name">Patrimoine Ajustement</field>
        <field name="code">patrimoine.ajustement</field>
        <field name="prefix">ADJ/</field>
        <field name="padding">5</field>
        <field name="number_next">1</field>
        <field name="number_increment">1</field>
    </record>
</odoo>

