from odoo import models, fields, api

class PatrimoineFacture(models.Model):
    _name = 'patrimoine.facture'
    _description = 'Facture Patrimoniale'

    name = fields.Char('N° Facture', required=True, default='/')
    date_facture = fields.Date('Date Facture', required=True, default=fields.Date.today)
    fournisseur_id = fields.Many2one('res.partner', 'Fournisseur', required=True)
    
    montant_ht = fields.Float('Montant HT')
    montant_tva = fields.Float('Montant TVA')
    montant_ttc = fields.Float('Montant TTC', compute='_compute_montant_ttc')
    
    state = fields.Selection([
        ('draft', 'Brouillon'),
        ('validated', 'Validée'),
        ('liquidated', 'Liquidée')
    ], default='draft')
    
    # Relations
    bien_ids = fields.Many2many('patrimoine.bien', string='Biens Concernés')
    account_move_id = fields.Many2one('account.move', 'Écriture Comptable')
    
    @api.depends('montant_ht', 'montant_tva')
    def _compute_montant_ttc(self):
        for record in self:
            record.montant_ttc = record.montant_ht + record.montant_tva
    
    @api.model
    def create(self, vals):
        if vals.get('name', '/') == '/':
            vals['name'] = self.env['ir.sequence'].next_by_code('patrimoine.facture')
        return super().create(vals)
    
    def action_validate(self):
        self.state = 'validated'
    
    def action_liquidate(self):
        self.state = 'liquidated'
        self._create_account_move()
    
    def _create_account_move(self):
        # Logique de création d'écriture comptable
        pass
    

