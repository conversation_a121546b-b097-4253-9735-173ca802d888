from odoo import models, fields, api

class PatrimoineDetenteur(models.Model):
    _name = 'patrimoine.detenteur'
    _description = 'Détenteur de Biens Patrimoniaux'

    name = fields.Char('Nom', required=True)
    employee_id = fields.Many2one('hr.employee', 'Employé')
    department_id = fields.Many2one('hr.department', 'Département')
    
    # Relations
    bien_ids = fields.One2many('patrimoine.bien', 'detenteur_actuel_id', 'Biens Détenus')
    total_biens = fields.Integer('Total Biens', compute='_compute_total_biens')
    
    @api.depends('bien_ids')
    def _compute_total_biens(self):
        for record in self:
            record.total_biens = len(record.bien_ids)


