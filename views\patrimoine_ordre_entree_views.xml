<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Vue formulaire des ordres d'entrée -->
    <record id="patrimoine_ordre_entree_form_view" model="ir.ui.view">
        <field name="name">patrimoine.ordre.entree.form</field>
        <field name="model">patrimoine.ordre.entree</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button name="action_confirm" string="Confirmer" type="object" class="btn-primary" states="draft"/>
                    <button name="action_receive" string="Réceptionner" type="object" class="btn-success" states="confirmed"/>
                    <field name="state" widget="statusbar"/>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="date_ordre"/>
                            <field name="fournisseur_id"/>
                            <field name="bon_commande"/>
                        </group>
                        <group>
                            <field name="bien_id"/>
                            <field name="quantite"/>
                            <field name="prix_unitaire"/>
                            <field name="montant_total"/>
                        </group>
                    </group>
                    <group>
                        <group>
                            <field name="date_livraison_prevue"/>
                            <field name="date_livraison_effective"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Vue liste des ordres d'entrée -->
    <record id="patrimoine_ordre_entree_tree_view" model="ir.ui.view">
        <field name="name">patrimoine.ordre.entree.tree</field>
        <field name="model">patrimoine.ordre.entree</field>
        <field name="arch" type="xml">
            <tree>
                <field name="name"/>
                <field name="date_ordre"/>
                <field name="fournisseur_id"/>
                <field name="bien_id"/>
                <field name="montant_total"/>
                <field name="state"/>
            </tree>
        </field>
    </record>

    <!-- Action pour les ordres d'entrée -->
    <record id="patrimoine_ordre_entree_action" model="ir.actions.act_window">
        <field name="name">Ordres d'Entrée</field>
        <field name="res_model">patrimoine.ordre.entree</field>
        <field name="view_mode">tree,form</field>
    </record>
</odoo>

