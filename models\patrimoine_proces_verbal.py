from odoo import models, fields, api

class PatrimoineProcessVerbal(models.Model):
    _name = 'patrimoine.proces.verbal'
    _description = 'Procès-Verbal Patrimonial'

    name = fields.Char('N° PV', required=True, default='/')
    date_proces_verbal = fields.Date('Date PV', required=True, default=fields.Date.today)
    
    type_proces_verbal = fields.Selection([
        ('reception', 'Réception'),
        ('reforme', 'Réforme'),
        ('inventaire', 'Inventaire'),
        ('cession', 'Cession')
    ], required=True)
    
    bien_id = fields.Many2one('patrimoine.bien', '<PERSON>ien Concerné')
    president_commission = fields.Char('Président de Commission')
    membres_commission = fields.Text('Membres de la Commission')
    description = fields.Text('Description')
    
    state = fields.Selection([
        ('draft', 'Brouillon'),
        ('validated', 'Validé'),
        ('archived', 'Archivé')
    ], default='draft')
    
    @api.model
    def create(self, vals):
        if vals.get('name', '/') == '/':
            vals['name'] = self.env['ir.sequence'].next_by_code('patrimoine.proces.verbal')
        return super().create(vals)
    
    def action_validate(self):
        self.state = 'validated'
    
    def action_archive(self):
        self.state = 'archived'








