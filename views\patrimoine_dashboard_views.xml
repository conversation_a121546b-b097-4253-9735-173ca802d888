<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Vue kanban pour le dashboard des biens -->
    <record id="patrimoine_bien_kanban_view" model="ir.ui.view">
        <field name="name">patrimoine.bien.kanban</field>
        <field name="model">patrimoine.bien</field>
        <field name="arch" type="xml">
            <kanban class="o_kanban_dashboard" create="false">
                <field name="etat"/>
                <field name="valeur_acquisition"/>
                <field name="detenteur_actuel_id"/>
                <field name="numero_inventaire"/>
                <field name="designation"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_card oe_kanban_global_click">
                            <div class="o_kanban_card_header">
                                <div class="o_kanban_card_header_title">
                                    <div class="o_primary">
                                        <field name="numero_inventaire"/>
                                    </div>
                                    <div class="o_secondary">
                                        <field name="designation"/>
                                    </div>
                                </div>
                            </div>
                            <div class="o_kanban_card_content">
                                <div class="row">
                                    <div class="col-6">
                                        <span class="badge badge-pill badge-primary">
                                            <field name="etat"/>
                                        </span>
                                    </div>
                                    <div class="col-6 text-right">
                                        <field name="valeur_acquisition" widget="monetary"/>
                                    </div>
                                </div>
                                <div class="row mt-2" t-if="record.detenteur_actuel_id.raw_value">
                                    <div class="col-12">
                                        <i class="fa fa-user"/> <field name="detenteur_actuel_id"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- Action dashboard pour les biens -->
    <record id="patrimoine_dashboard_action" model="ir.actions.act_window">
        <field name="name">Tableau de Bord Patrimonial</field>
        <field name="res_model">patrimoine.bien</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="view_id" ref="patrimoine_bien_kanban_view"/>
        <field name="context">{'group_by': 'etat'}</field>
    </record>

    <!-- Vue graphique pour les statistiques -->
    <record id="patrimoine_bien_graph_view" model="ir.ui.view">
        <field name="name">patrimoine.bien.graph</field>
        <field name="model">patrimoine.bien</field>
        <field name="arch" type="xml">
            <graph string="Répartition des Biens" type="pie">
                <field name="etat"/>
            </graph>
        </field>
    </record>

    <!-- Vue pivot pour les analyses -->
    <record id="patrimoine_bien_pivot_view" model="ir.ui.view">
        <field name="name">patrimoine.bien.pivot</field>
        <field name="model">patrimoine.bien</field>
        <field name="arch" type="xml">
            <pivot string="Analyse des Biens">
                <field name="etat" type="row"/>
                <field name="detenteur_actuel_id" type="col"/>
                <field name="valeur_acquisition" type="measure"/>
            </pivot>
        </field>
    </record>

    <!-- Action pour les statistiques -->
    <record id="patrimoine_statistiques_action" model="ir.actions.act_window">
        <field name="name">Statistiques Patrimoniales</field>
        <field name="res_model">patrimoine.bien</field>
        <field name="view_mode">graph,pivot</field>
        <field name="view_id" ref="patrimoine_bien_graph_view"/>
    </record>

    <!-- Vue graphique pour les mouvements -->
    <record id="patrimoine_mouvement_graph_view" model="ir.ui.view">
        <field name="name">patrimoine.mouvement.graph</field>
        <field name="model">patrimoine.mouvement</field>
        <field name="arch" type="xml">
            <graph string="Mouvements par Type" type="bar">
                <field name="type_mouvement"/>
                <field name="date_mouvement" interval="month"/>
            </graph>
        </field>
    </record>

    <!-- Action pour les statistiques de mouvements -->
    <record id="patrimoine_mouvement_statistiques_action" model="ir.actions.act_window">
        <field name="name">Statistiques des Mouvements</field>
        <field name="res_model">patrimoine.mouvement</field>
        <field name="view_mode">graph,pivot</field>
        <field name="view_id" ref="patrimoine_mouvement_graph_view"/>
    </record>
</odoo>
