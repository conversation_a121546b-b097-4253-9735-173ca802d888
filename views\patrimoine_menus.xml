<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Menu principal -->
    <menuitem id="patrimoine_main_menu" name="Patrimoine" sequence="10" web_icon="patrimoine_management,static/description/icon.png"/>

    <!-- Sous-menus -->
    <menuitem id="patrimoine_dashboard_menu" name="Tableau de Bord" parent="patrimoine_main_menu"
              action="patrimoine_dashboard_action" sequence="5"/>
    <menuitem id="patrimoine_gestion_menu" name="Gestion" parent="patrimoine_main_menu" sequence="10"/>
    <menuitem id="patrimoine_rapports_menu" name="Rapports" parent="patrimoine_main_menu" sequence="20"/>
    <menuitem id="patrimoine_configuration_menu" name="Configuration" parent="patrimoine_main_menu" sequence="30"/>

    <!-- Menus de gestion -->
    <menuitem id="patrimoine_bien_menu" name="Biens Patrimoniaux" parent="patrimoine_gestion_menu"
              action="patrimoine_bien_action" sequence="10"/>
    <menuitem id="patrimoine_detenteur_menu" name="Détenteurs" parent="patrimoine_gestion_menu"
              action="patrimoine_detenteur_action" sequence="20"/>
    <menuitem id="patrimoine_mouvement_menu" name="Mouvements" parent="patrimoine_gestion_menu"
              action="patrimoine_mouvement_action" sequence="30"/>
    <menuitem id="patrimoine_ordre_entree_menu" name="Ordres d'Entrée" parent="patrimoine_gestion_menu"
              action="patrimoine_ordre_entree_action" sequence="40"/>
    <menuitem id="patrimoine_bon_sortie_menu" name="Bons de Sortie Provisoire" parent="patrimoine_gestion_menu"
              action="patrimoine_bon_sortie_action" sequence="50"/>
    <menuitem id="patrimoine_facture_menu" name="Factures" parent="patrimoine_gestion_menu"
              action="patrimoine_facture_action" sequence="60"/>
    <menuitem id="patrimoine_proces_verbal_menu" name="Procès-Verbaux" parent="patrimoine_gestion_menu"
              action="patrimoine_proces_verbal_action" sequence="70"/>
    <menuitem id="patrimoine_stock_menu" name="Fiches de Stock" parent="patrimoine_gestion_menu"
              action="patrimoine_stock_action" sequence="80"/>
    <menuitem id="patrimoine_ajustement_menu" name="Ajustements" parent="patrimoine_gestion_menu"
              action="patrimoine_ajustement_action" sequence="90"/>

    <!-- Menus de rapports -->
    <menuitem id="patrimoine_statistiques_menu" name="Statistiques" parent="patrimoine_rapports_menu"
              action="patrimoine_statistiques_action" sequence="5"/>
    <menuitem id="patrimoine_statistiques_mouvements_menu" name="Statistiques Mouvements" parent="patrimoine_rapports_menu"
              action="patrimoine_mouvement_statistiques_action" sequence="6"/>
    <menuitem id="patrimoine_livre_journal_menu" name="Livre Journal" parent="patrimoine_rapports_menu"
              action="action_livre_journal_report" sequence="10"/>
    <menuitem id="patrimoine_grand_livre_menu" name="Grand Livre" parent="patrimoine_rapports_menu"
              action="action_grand_livre_report" sequence="20"/>

    <!-- Sous-menus de rapports par catégorie -->
    <menuitem id="patrimoine_rapports_biens_menu" name="Rapports Biens" parent="patrimoine_rapports_menu" sequence="30"/>
    <menuitem id="patrimoine_rapports_mouvements_menu" name="Rapports Mouvements" parent="patrimoine_rapports_menu" sequence="40"/>

    <!-- Actions de rapports spécialisés -->
    <record id="action_rapport_biens_par_etat" model="ir.actions.act_window">
        <field name="name">Biens par État</field>
        <field name="res_model">patrimoine.bien</field>
        <field name="view_mode">tree</field>
        <field name="context">{'group_by': 'etat'}</field>
    </record>

    <record id="action_rapport_biens_par_detenteur" model="ir.actions.act_window">
        <field name="name">Biens par Détenteur</field>
        <field name="res_model">patrimoine.bien</field>
        <field name="view_mode">tree</field>
        <field name="context">{'group_by': 'detenteur_actuel_id'}</field>
    </record>

    <record id="action_rapport_mouvements_par_type" model="ir.actions.act_window">
        <field name="name">Mouvements par Type</field>
        <field name="res_model">patrimoine.mouvement</field>
        <field name="view_mode">tree</field>
        <field name="context">{'group_by': 'type_mouvement'}</field>
    </record>

    <!-- Menus pour les rapports spécialisés -->
    <menuitem id="patrimoine_rapport_biens_etat_menu" name="Biens par État" parent="patrimoine_rapports_biens_menu"
              action="action_rapport_biens_par_etat" sequence="10"/>
    <menuitem id="patrimoine_rapport_biens_detenteur_menu" name="Biens par Détenteur" parent="patrimoine_rapports_biens_menu"
              action="action_rapport_biens_par_detenteur" sequence="20"/>
    <menuitem id="patrimoine_rapport_mouvements_type_menu" name="Mouvements par Type" parent="patrimoine_rapports_mouvements_menu"
              action="action_rapport_mouvements_par_type" sequence="10"/>
</odoo>


