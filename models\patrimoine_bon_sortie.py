from odoo import models, fields, api

class PatrimoineBonSortie(models.Model):
    _name = 'patrimoine.bon.sortie'
    _description = 'Bon de Sortie Provisoire'

    name = fields.Char('N° Bon', required=True, default='/')
    date_sortie = fields.Date('Date Sortie', required=True, default=fields.Date.today)
    date_retour_prevue = fields.Date('Date Retour Prévue')
    date_retour_effective = fields.Date('Date Retour Effective')
    
    bien_id = fields.Many2one('patrimoine.bien', 'Bien', required=True)
    detenteur_id = fields.Many2one('patrimoine.detenteur', 'Détenteur', required=True)
    lieu_utilisation = fields.Char('Lieu d\'Utilisation')
    motif = fields.Text('Motif')
    
    state = fields.Selection([
        ('draft', 'Brouillon'),
        ('confirmed', 'Confirmé'),
        ('out', 'Sorti'),
        ('returned', 'Retourné'),
        ('cancelled', 'Annulé')
    ], default='draft')
    
    @api.model
    def create(self, vals):
        if vals.get('name', '/') == '/':
            vals['name'] = self.env['ir.sequence'].next_by_code('patrimoine.bon.sortie')
        return super().create(vals)
    
    def action_confirm(self):
        self.state = 'confirmed'
    
    def action_sortir(self):
        self.state = 'out'
        # Créer un mouvement de sortie
        self.env['patrimoine.mouvement'].create({
            'type_mouvement': 'sortie',
            'bien_id': self.bien_id.id,
            'detenteur_destination_id': self.detenteur_id.id,
            'bon_sortie_provisoire': True,
            'date_retour_prevue': self.date_retour_prevue,
            'motif': self.motif,
            'state': 'done'
        })
    
    def action_retourner(self):
        self.state = 'returned'
        self.date_retour_effective = fields.Date.today()
        # Créer un mouvement de retour
        self.env['patrimoine.mouvement'].create({
            'type_mouvement': 'entree',
            'bien_id': self.bien_id.id,
            'detenteur_origine_id': self.detenteur_id.id,
            'motif': f'Retour bon sortie {self.name}',
            'state': 'done'
        })



