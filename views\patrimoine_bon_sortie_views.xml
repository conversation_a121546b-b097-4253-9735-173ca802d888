<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Vue formulaire des bons de sortie -->
    <record id="patrimoine_bon_sortie_form_view" model="ir.ui.view">
        <field name="name">patrimoine.bon.sortie.form</field>
        <field name="model">patrimoine.bon.sortie</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button name="action_confirm" string="Confirmer" type="object" class="btn-primary" states="draft"/>
                    <button name="action_sortir" string="Sortir" type="object" class="btn-warning" states="confirmed"/>
                    <button name="action_retourner" string="Retourner" type="object" class="btn-success" states="out"/>
                    <field name="state" widget="statusbar"/>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="date_sortie"/>
                            <field name="date_retour_prevue"/>
                            <field name="date_retour_effective"/>
                        </group>
                        <group>
                            <field name="bien_id"/>
                            <field name="detenteur_id"/>
                            <field name="lieu_utilisation"/>
                        </group>
                    </group>
                    <group>
                        <field name="motif"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Vue liste des bons de sortie -->
    <record id="patrimoine_bon_sortie_tree_view" model="ir.ui.view">
        <field name="name">patrimoine.bon.sortie.tree</field>
        <field name="model">patrimoine.bon.sortie</field>
        <field name="arch" type="xml">
            <tree>
                <field name="name"/>
                <field name="date_sortie"/>
                <field name="bien_id"/>
                <field name="detenteur_id"/>
                <field name="state"/>
            </tree>
        </field>
    </record>

    <!-- Action pour les bons de sortie -->
    <record id="patrimoine_bon_sortie_action" model="ir.actions.act_window">
        <field name="name">Bons de Sortie</field>
        <field name="res_model">patrimoine.bon.sortie</field>
        <field name="view_mode">tree,form</field>
    </record>
</odoo>



