<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Vue formulaire des factures -->
    <record id="patrimoine_facture_form_view" model="ir.ui.view">
        <field name="name">patrimoine.facture.form</field>
        <field name="model">patrimoine.facture</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button name="action_validate" string="Valider" type="object" 
                            class="btn-primary" states="draft"/>
                    <button name="action_liquidate" string="Liquider" type="object" 
                            class="btn-primary" states="validated"/>
                    <field name="state" widget="statusbar"/>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="date_facture"/>
                            <field name="fournisseur_id"/>
                        </group>
                        <group>
                            <field name="montant_ht"/>
                            <field name="montant_tva"/>
                            <field name="montant_ttc"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Biens Concernés">
                            <field name="bien_ids"/>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Vue liste des factures -->
    <record id="patrimoine_facture_tree_view" model="ir.ui.view">
        <field name="name">patrimoine.facture.tree</field>
        <field name="model">patrimoine.facture</field>
        <field name="arch" type="xml">
            <tree>
                <field name="name"/>
                <field name="date_facture"/>
                <field name="fournisseur_id"/>
                <field name="montant_ttc"/>
                <field name="state"/>
            </tree>
        </field>
    </record>

    <!-- Action pour les factures -->
    <record id="patrimoine_facture_action" model="ir.actions.act_window">
        <field name="name">Factures</field>
        <field name="res_model">patrimoine.facture</field>
        <field name="view_mode">tree,form</field>
    </record>
</odoo>




