<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Vue formulaire des biens -->
    <record id="patrimoine_bien_form_view" model="ir.ui.view">
        <field name="name">patrimoine.bien.form</field>
        <field name="model">patrimoine.bien</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button name="action_mettre_en_attente" string="Mettre en Attente" type="object" class="btn-warning" states="stock,actif"/>
                    <button name="action_reformer" string="Réformer" type="object" class="btn-danger" states="stock,actif,attente"/>
                    <button name="action_mettre_en_reparation" string="Mettre en Réparation" type="object" class="btn-info" states="stock,actif"/>
                    <button name="action_ceder" string="Céder" type="object" class="btn-secondary" states="stock,attente"/>
                    <button name="action_declarer_perdu" string="Déclarer Perdu" type="object" class="btn-danger" states="stock,actif,attente"/>
                    <field name="etat" widget="statusbar"/>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="numero_inventaire"/>
                            <field name="designation"/>
                            <field name="marque"/>
                            <field name="modele"/>
                            <field name="numero_serie"/>
                        </group>
                        <group>
                            <field name="valeur_acquisition"/>
                            <field name="valeur_actuelle"/>
                            <field name="quantite_stock"/>
                            <field name="quantite_disponible"/>
                        </group>
                    </group>
                    <group>
                        <group>
                            <field name="date_acquisition"/>
                            <field name="date_mise_service"/>
                            <field name="localisation"/>
                            <field name="detenteur_actuel_id"/>
                        </group>
                        <group>
                            <field name="date_reforme" attrs="{'invisible': [('etat', '!=', 'reforme')]}"/>
                            <field name="date_cession" attrs="{'invisible': [('etat', '!=', 'cede')]}"/>
                            <field name="date_perte" attrs="{'invisible': [('etat', '!=', 'perdu')]}"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Mouvements">
                            <field name="mouvement_ids">
                                <tree>
                                    <field name="date_mouvement"/>
                                    <field name="type_mouvement"/>
                                    <field name="motif"/>
                                    <field name="state"/>
                                </tree>
                            </field>
                        </page>
                        <page string="Procès-Verbaux">
                            <field name="proces_verbal_ids">
                                <tree>
                                    <field name="name"/>
                                    <field name="date_proces_verbal"/>
                                    <field name="type_proces_verbal"/>
                                    <field name="state"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Vue liste des biens -->
    <record id="patrimoine_bien_tree_view" model="ir.ui.view">
        <field name="name">patrimoine.bien.tree</field>
        <field name="model">patrimoine.bien</field>
        <field name="arch" type="xml">
            <tree>
                <field name="numero_inventaire"/>
                <field name="designation"/>
                <field name="etat"/>
                <field name="detenteur_actuel_id"/>
                <field name="valeur_acquisition"/>
                <field name="localisation"/>
            </tree>
        </field>
    </record>

    <!-- Vue recherche des biens -->
    <record id="patrimoine_bien_search_view" model="ir.ui.view">
        <field name="name">patrimoine.bien.search</field>
        <field name="model">patrimoine.bien</field>
        <field name="arch" type="xml">
            <search>
                <field name="numero_inventaire"/>
                <field name="designation"/>
                <field name="marque"/>
                <field name="modele"/>
                <field name="detenteur_actuel_id"/>
                <field name="localisation"/>
                <separator/>
                <filter name="actif" string="Actifs" domain="[('etat', '=', 'actif')]"/>
                <filter name="stock" string="En Stock" domain="[('etat', '=', 'stock')]"/>
                <filter name="attente" string="En Attente" domain="[('etat', '=', 'attente')]"/>
                <filter name="reforme" string="Réformés" domain="[('etat', '=', 'reforme')]"/>
                <filter name="reparation" string="En Réparation" domain="[('etat', '=', 'reparation')]"/>
                <separator/>
                <filter name="avec_detenteur" string="Avec Détenteur" domain="[('detenteur_actuel_id', '!=', False)]"/>
                <filter name="sans_detenteur" string="Sans Détenteur" domain="[('detenteur_actuel_id', '=', False)]"/>
                <separator/>
                <group expand="0" string="Grouper par">
                    <filter name="group_etat" string="État" context="{'group_by': 'etat'}"/>
                    <filter name="group_detenteur" string="Détenteur" context="{'group_by': 'detenteur_actuel_id'}"/>
                    <filter name="group_localisation" string="Localisation" context="{'group_by': 'localisation'}"/>
                    <filter name="group_marque" string="Marque" context="{'group_by': 'marque'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action pour les biens -->
    <record id="patrimoine_bien_action" model="ir.actions.act_window">
        <field name="name">Biens Patrimoniaux</field>
        <field name="res_model">patrimoine.bien</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="patrimoine_bien_search_view"/>
        <field name="context">{'search_default_actif': 1, 'search_default_stock': 1}</field>
    </record>
</odoo>




