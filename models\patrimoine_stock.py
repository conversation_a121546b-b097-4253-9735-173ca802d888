from odoo import models, fields, api

class PatrimoineStock(models.Model):
    _name = 'patrimoine.stock'
    _description = 'Fiche de Stock Patrimonial'

    name = fields.Char('Référence', required=True, default='/')
    bien_id = fields.Many2one('patrimoine.bien', 'Bien', required=True)
    date_inventaire = fields.Date('Date Inventaire', required=True, default=fields.Date.today)
    
    quantite_theorique = fields.Float('Quantité Théorique', default=0.0)
    quantite_physique = fields.Float('Quantité Physique', default=0.0)
    ecart = fields.Float('Écart', compute='_compute_ecart')
    
    @api.depends('quantite_theorique', 'quantite_physique')
    def _compute_ecart(self):
        for record in self:
            record.ecart = record.quantite_physique - record.quantite_theorique
    
    @api.model
    def create(self, vals):
        if vals.get('name', '/') == '/':
            vals['name'] = self.env['ir.sequence'].next_by_code('patrimoine.stock')
        return super().create(vals)




