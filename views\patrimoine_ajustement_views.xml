<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Vue formulaire des ajustements -->
    <record id="patrimoine_ajustement_form_view" model="ir.ui.view">
        <field name="name">patrimoine.ajustement.form</field>
        <field name="model">patrimoine.ajustement</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button name="action_confirm" string="Confirmer" type="object" class="btn-primary" states="draft"/>
                    <button name="action_done" string="Effectuer" type="object" class="btn-success" states="confirmed"/>
                    <field name="state" widget="statusbar"/>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="date_ajustement"/>
                            <field name="type_ajustement"/>
                            <field name="bien_id"/>
                        </group>
                    </group>
                    <group string="Ajustement Stock" attrs="{'invisible': [('type_ajustement', '!=', 'stock')]}">
                        <group>
                            <field name="quantite_theorique"/>
                            <field name="quantite_reelle"/>
                            <field name="ecart_quantite"/>
                        </group>
                    </group>
                    <group string="Changement Détenteur" attrs="{'invisible': [('type_ajustement', '!=', 'detenteur')]}">
                        <group>
                            <field name="ancien_detenteur_id"/>
                            <field name="nouveau_detenteur_id"/>
                        </group>
                    </group>
                    <group string="Ajustement Valeur" attrs="{'invisible': [('type_ajustement', '!=', 'valeur')]}">
                        <group>
                            <field name="ancienne_valeur"/>
                            <field name="nouvelle_valeur"/>
                        </group>
                    </group>
                    <group>
                        <field name="motif"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Vue liste des ajustements -->
    <record id="patrimoine_ajustement_tree_view" model="ir.ui.view">
        <field name="name">patrimoine.ajustement.tree</field>
        <field name="model">patrimoine.ajustement</field>
        <field name="arch" type="xml">
            <tree>
                <field name="name"/>
                <field name="date_ajustement"/>
                <field name="type_ajustement"/>
                <field name="bien_id"/>
                <field name="state"/>
            </tree>
        </field>
    </record>

    <!-- Action pour les ajustements -->
    <record id="patrimoine_ajustement_action" model="ir.actions.act_window">
        <field name="name">Ajustements</field>
        <field name="res_model">patrimoine.ajustement</field>
        <field name="view_mode">tree,form</field>
    </record>
</odoo>




