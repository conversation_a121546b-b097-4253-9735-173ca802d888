from odoo import models, fields, api

class PatrimoineMouvement(models.Model):
    _name = 'patrimoine.mouvement'
    _description = 'Mouvement Patrimonial'

    name = fields.Char('N° Mouvement', required=True, default='/')
    date_mouvement = fields.Date('Date Mouvement', required=True, default=fields.Date.today)
    
    type_mouvement = fields.Selection([
        ('entree', 'Entrée'),
        ('sortie', 'Sortie'),
        ('transfert', 'Transfert'),
        ('reforme', 'Réforme'),
        ('reparation', 'Réparation'),
        ('cession', 'Cession'),
        ('perte', 'Perte'),
        ('ajustement', 'Ajustement')
    ], required=True)
    
    bien_id = fields.Many2one('patrimoine.bien', 'Bien', required=True)
    quantite = fields.Float('Quantité', default=1.0)
    
    # Détenteurs
    detenteur_origine_id = fields.Many2one('patrimoine.detenteur', 'Détenteur Origine')
    detenteur_destination_id = fields.Many2one('patrimoine.detenteur', 'Détenteur Destination')
    
    # Sortie provisoire
    bon_sortie_provisoire = fields.Boolean('Sortie Provisoire')
    date_retour_prevue = fields.Date('Date Retour Prévue')
    
    motif = fields.Text('Motif')
    
    # Relations
    ordre_entree_id = fields.Many2one('patrimoine.ordre.entree', 'Ordre d\'Entrée')
    
    state = fields.Selection([
        ('draft', 'Brouillon'),
        ('confirmed', 'Confirmé'),
        ('done', 'Effectué')
    ], default='draft')
    
    @api.model
    def create(self, vals):
        if vals.get('name', '/') == '/':
            vals['name'] = self.env['ir.sequence'].next_by_code('patrimoine.mouvement')
        return super().create(vals)
    
    def action_confirm(self):
        self.state = 'confirmed'
    
    def action_done(self):
        self.state = 'done'
        # Logique de mise à jour du bien selon le type de mouvement
        if self.type_mouvement == 'transfert' and self.detenteur_destination_id:
            self.bien_id.detenteur_actuel_id = self.detenteur_destination_id




