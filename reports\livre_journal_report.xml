<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Action pour le livre journal -->
    <record id="action_livre_journal_report" model="ir.actions.report">
        <field name="name">Livre Journal Patrimonial</field>
        <field name="model">patrimoine.mouvement</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">patrimoine_management.livre_journal_template</field>
        <field name="report_file">patrimoine_management.livre_journal_template</field>
        <field name="binding_model_id" ref="model_patrimoine_mouvement"/>
        <field name="binding_type">report</field>
    </record>

    <!-- Template du livre journal -->
    <template id="livre_journal_template">
        <t t-call="web.html_container">
            <t t-foreach="docs" t-as="o">
                <div class="page">
                    <h2>Livre Journal Patrimonial</h2>
                    <table class="table table-condensed">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Réf<PERSON>rence</th>
                                <th>Type</th>
                                <th>Bien</th>
                                <th>Motif</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><span t-field="o.date_mouvement"/></td>
                                <td><span t-field="o.name"/></td>
                                <td><span t-field="o.type_mouvement"/></td>
                                <td><span t-field="o.bien_id.designation"/></td>
                                <td><span t-field="o.motif"/></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </t>
        </t>
    </template>
</odoo>


