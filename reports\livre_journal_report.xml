<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Action pour le livre journal -->
    <record id="action_livre_journal_report" model="ir.actions.report">
        <field name="name">Livre Journal Patrimonial</field>
        <field name="model">patrimoine.mouvement</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">patrimoine_management.livre_journal_template</field>
        <field name="report_file">patrimoine_management.livre_journal_template</field>
        <field name="binding_model_id" ref="model_patrimoine_mouvement"/>
        <field name="binding_type">report</field>
    </record>

    <!-- Template du livre journal -->
    <template id="livre_journal_template">
        <t t-call="web.html_container">
            <div class="header">
                <div class="row">
                    <div class="col-6">
                        <img t-if="company.logo" t-att-src="image_data_uri(company.logo)" style="max-height: 45px;" alt="Logo"/>
                    </div>
                    <div class="col-6 text-right">
                        <h4 t-field="company.name"/>
                        <div t-field="company.partner_id" t-options='{"widget": "contact", "fields": ["address"], "no_marker": true}'/>
                    </div>
                </div>
            </div>

            <div class="page">
                <h2 class="text-center">Livre Journal Patrimonial</h2>
                <div class="row mt-4 mb-4">
                    <div class="col-6">
                        <strong>Période:</strong> <span t-esc="context.get('date_from', 'Début')"/> - <span t-esc="context.get('date_to', 'Fin')"/>
                    </div>
                    <div class="col-6 text-right">
                        <strong>Date d'impression:</strong> <span t-esc="context_today().strftime('%d/%m/%Y')"/>
                    </div>
                </div>

                <table class="table table-sm table-bordered">
                    <thead class="thead-light">
                        <tr>
                            <th>Date</th>
                            <th>Référence</th>
                            <th>Type</th>
                            <th>Bien</th>
                            <th>N° Inventaire</th>
                            <th>Détenteur Origine</th>
                            <th>Détenteur Destination</th>
                            <th>Quantité</th>
                            <th>Motif</th>
                            <th>État</th>
                        </tr>
                    </thead>
                    <tbody>
                        <t t-foreach="docs" t-as="o">
                            <tr>
                                <td><span t-field="o.date_mouvement"/></td>
                                <td><span t-field="o.name"/></td>
                                <td><span t-field="o.type_mouvement"/></td>
                                <td><span t-field="o.bien_id.designation"/></td>
                                <td><span t-field="o.bien_id.numero_inventaire"/></td>
                                <td><span t-field="o.detenteur_origine_id.name"/></td>
                                <td><span t-field="o.detenteur_destination_id.name"/></td>
                                <td class="text-right"><span t-field="o.quantite"/></td>
                                <td><span t-field="o.motif"/></td>
                                <td>
                                    <span t-field="o.state" t-options='{"widget": "badge"}'/>
                                </td>
                            </tr>
                        </t>
                    </tbody>
                </table>

                <div class="row mt-4">
                    <div class="col-12">
                        <p><strong>Total des mouvements:</strong> <span t-esc="len(docs)"/></p>
                    </div>
                </div>
            </div>
        </t>
    </template>
</odoo>


