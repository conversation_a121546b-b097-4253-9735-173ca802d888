from odoo import models, fields, api

class PatrimoineOrdreEntree(models.Model):
    _name = 'patrimoine.ordre.entree'
    _description = 'Ordre d\'Entrée Patrimonial'

    name = fields.Char('N° Ordre', required=True, default='/')
    date_ordre = fields.Date('Date Ordre', required=True, default=fields.Date.today)
    fournisseur_id = fields.Many2one('res.partner', 'Fournisseur', required=True)
    bon_commande = fields.Char('Bon de Commande')
    
    bien_id = fields.Many2one('patrimoine.bien', 'Bien', required=True)
    quantite = fields.Float('Quantité', default=1.0)
    prix_unitaire = fields.Float('Prix Unitaire')
    montant_total = fields.Float('Montant Total', compute='_compute_montant_total')
    
    date_livraison_prevue = fields.Date('Date Livraison Prévue')
    date_livraison_effective = fields.Date('Date Livraison Effective')
    
    state = fields.Selection([
        ('draft', 'Brouillon'),
        ('confirmed', 'Confirmé'),
        ('received', 'Reçu'),
        ('cancelled', 'Annulé')
    ], default='draft')
    
    @api.depends('quantite', 'prix_unitaire')
    def _compute_montant_total(self):
        for record in self:
            record.montant_total = record.quantite * record.prix_unitaire
    
    @api.model
    def create(self, vals):
        if vals.get('name', '/') == '/':
            vals['name'] = self.env['ir.sequence'].next_by_code('patrimoine.ordre.entree')
        return super().create(vals)
    
    def action_confirm(self):
        self.state = 'confirmed'
    
    def action_receive(self):
        self.state = 'received'
        self.date_livraison_effective = fields.Date.today()
        # Créer un mouvement d'entrée
        self.env['patrimoine.mouvement'].create({
            'type_mouvement': 'entree',
            'bien_id': self.bien_id.id,
            'quantite': self.quantite,
            'ordre_entree_id': self.id,
            'motif': f'Réception ordre {self.name}',
            'state': 'done'
        })






