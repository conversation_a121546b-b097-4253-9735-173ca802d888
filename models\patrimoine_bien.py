from odoo import models, fields, api
from odoo.exceptions import ValidationError, UserError

class PatrimoineBien(models.Model):
    _name = 'patrimoine.bien'
    _description = 'Bien Patrimonial'

    name = fields.Char('Nom', compute='_compute_name', store=True)
    numero_inventaire = fields.Char('N° Inventaire', required=True, default='/')
    designation = fields.Char('Désignation', required=True)
    marque = fields.Char('Marque')
    modele = fields.Char('Modèle')
    numero_serie = fields.Char('N° Série')
    
    # Valeurs
    valeur_acquisition = fields.Float('Valeur d\'Acquisition')
    valeur_actuelle = fields.Float('Valeur Actuelle')
    
    # Dates
    date_acquisition = fields.Date('Date d\'Acquisition')
    date_mise_service = fields.Date('Date Mise en Service')
    date_reforme = fields.Date('Date Réforme')
    date_cession = fields.Date('Date Cession')
    date_perte = fields.Date('Date Perte')
    
    # Stock
    quantite_stock = fields.Float('Quantité en Stock', default=1.0)
    quantite_disponible = fields.Float('Quantité Disponible', compute='_compute_quantite_disponible')
    
    # Localisation
    localisation = fields.Char('Localisation')
    detenteur_actuel_id = fields.Many2one('patrimoine.detenteur', 'Détenteur Actuel')
    
    # États du bien
    etat = fields.Selection([
        ('actif', 'Actif'),
        ('attente', 'En Attente'),
        ('reforme', 'Réformé'),
        ('reparation', 'En Réparation'),
        ('cede', 'Cédé'),
        ('perdu', 'Perdu'),
        ('stock', 'En Stock')
    ], default='stock', required=True)
    
    # Relations
    mouvement_ids = fields.One2many('patrimoine.mouvement', 'bien_id', 'Mouvements')
    proces_verbal_ids = fields.One2many('patrimoine.proces.verbal', 'bien_id', 'Procès-Verbaux')
    ordre_entree_ids = fields.One2many('patrimoine.ordre.entree', 'bien_id', 'Ordres d\'Entrée')
    
    @api.depends('designation', 'numero_inventaire')
    def _compute_name(self):
        for record in self:
            record.name = f"[{record.numero_inventaire}] {record.designation}"
    
    @api.depends('quantite_stock', 'mouvement_ids')
    def _compute_quantite_disponible(self):
        for record in self:
            record.quantite_disponible = record.quantite_stock

    @api.model
    def create(self, vals):
        if vals.get('numero_inventaire', '/') == '/':
            vals['numero_inventaire'] = self.env['ir.sequence'].next_by_code('patrimoine.bien')
        return super().create(vals)
    
    @api.constrains('valeur_acquisition', 'valeur_actuelle')
    def _check_valeurs(self):
        for record in self:
            if record.valeur_acquisition < 0:
                raise ValidationError("La valeur d'acquisition ne peut pas être négative.")
            if record.valeur_actuelle < 0:
                raise ValidationError("La valeur actuelle ne peut pas être négative.")

    @api.constrains('quantite_stock')
    def _check_quantite_stock(self):
        for record in self:
            if record.quantite_stock < 0:
                raise ValidationError("La quantité en stock ne peut pas être négative.")

    @api.constrains('date_acquisition', 'date_mise_service', 'date_reforme', 'date_cession', 'date_perte')
    def _check_dates(self):
        for record in self:
            today = fields.Date.today()
            if record.date_acquisition and record.date_acquisition > today:
                raise ValidationError("La date d'acquisition ne peut pas être dans le futur.")
            if record.date_mise_service and record.date_acquisition and record.date_mise_service < record.date_acquisition:
                raise ValidationError("La date de mise en service ne peut pas être antérieure à la date d'acquisition.")

    def action_mettre_en_attente(self):
        if self.etat not in ['stock', 'actif']:
            raise UserError("Seuls les biens en stock ou actifs peuvent être mis en attente.")
        self.etat = 'attente'

    def action_reformer(self):
        if self.etat in ['reforme', 'cede', 'perdu']:
            raise UserError("Ce bien ne peut plus être réformé.")
        self.etat = 'reforme'
        self.date_reforme = fields.Date.today()
        # Créer un mouvement
        self.env['patrimoine.mouvement'].create({
            'type_mouvement': 'reforme',
            'bien_id': self.id,
            'motif': 'Réforme du bien',
            'state': 'done'
        })

    def action_mettre_en_reparation(self):
        if self.etat not in ['stock', 'actif']:
            raise UserError("Seuls les biens en stock ou actifs peuvent être mis en réparation.")
        self.etat = 'reparation'
        # Créer un mouvement
        self.env['patrimoine.mouvement'].create({
            'type_mouvement': 'reparation',
            'bien_id': self.id,
            'motif': 'Mise en réparation',
            'state': 'done'
        })

    def action_ceder(self):
        if self.etat in ['reforme', 'cede', 'perdu']:
            raise UserError("Ce bien ne peut plus être cédé.")
        self.etat = 'cede'
        self.date_cession = fields.Date.today()
        # Créer un mouvement
        self.env['patrimoine.mouvement'].create({
            'type_mouvement': 'cession',
            'bien_id': self.id,
            'motif': 'Cession du bien',
            'state': 'done'
        })

    def action_declarer_perdu(self):
        if self.etat in ['reforme', 'cede', 'perdu']:
            raise UserError("Ce bien ne peut plus être déclaré perdu.")
        self.etat = 'perdu'
        self.date_perte = fields.Date.today()
        # Créer un mouvement
        self.env['patrimoine.mouvement'].create({
            'type_mouvement': 'perte',
            'bien_id': self.id,
            'motif': 'Déclaration de perte',
            'state': 'done'
        })













