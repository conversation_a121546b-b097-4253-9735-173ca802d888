<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Vue formulaire des mouvements -->
    <record id="patrimoine_mouvement_form_view" model="ir.ui.view">
        <field name="name">patrimoine.mouvement.form</field>
        <field name="model">patrimoine.mouvement</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button name="action_confirm" string="Confirmer" type="object" class="btn-primary" states="draft"/>
                    <button name="action_done" string="Effectuer" type="object" class="btn-success" states="confirmed"/>
                    <field name="state" widget="statusbar"/>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="date_mouvement"/>
                            <field name="type_mouvement"/>
                            <field name="bien_id"/>
                        </group>
                        <group>
                            <field name="quantite"/>
                            <field name="detenteur_origine_id"/>
                            <field name="detenteur_destination_id"/>
                        </group>
                    </group>
                    <group>
                        <field name="motif"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Vue liste des mouvements -->
    <record id="patrimoine_mouvement_tree_view" model="ir.ui.view">
        <field name="name">patrimoine.mouvement.tree</field>
        <field name="model">patrimoine.mouvement</field>
        <field name="arch" type="xml">
            <tree>
                <field name="name"/>
                <field name="date_mouvement"/>
                <field name="type_mouvement"/>
                <field name="bien_id"/>
                <field name="state"/>
            </tree>
        </field>
    </record>

    <!-- Action pour les mouvements -->
    <record id="patrimoine_mouvement_action" model="ir.actions.act_window">
        <field name="name">Mouvements</field>
        <field name="res_model">patrimoine.mouvement</field>
        <field name="view_mode">tree,form</field>
    </record>
</odoo>



