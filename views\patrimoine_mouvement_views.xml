<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Vue formulaire des mouvements -->
    <record id="patrimoine_mouvement_form_view" model="ir.ui.view">
        <field name="name">patrimoine.mouvement.form</field>
        <field name="model">patrimoine.mouvement</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button name="action_confirm" string="Confirmer" type="object" class="btn-primary" states="draft"/>
                    <button name="action_done" string="Effectuer" type="object" class="btn-success" states="confirmed"/>
                    <field name="state" widget="statusbar"/>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="date_mouvement"/>
                            <field name="type_mouvement"/>
                            <field name="bien_id"/>
                        </group>
                        <group>
                            <field name="quantite"/>
                            <field name="detenteur_origine_id"/>
                            <field name="detenteur_destination_id"/>
                        </group>
                    </group>
                    <group>
                        <field name="motif"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Vue liste des mouvements -->
    <record id="patrimoine_mouvement_tree_view" model="ir.ui.view">
        <field name="name">patrimoine.mouvement.tree</field>
        <field name="model">patrimoine.mouvement</field>
        <field name="arch" type="xml">
            <tree>
                <field name="name"/>
                <field name="date_mouvement"/>
                <field name="type_mouvement"/>
                <field name="bien_id"/>
                <field name="state"/>
            </tree>
        </field>
    </record>

    <!-- Vue recherche des mouvements -->
    <record id="patrimoine_mouvement_search_view" model="ir.ui.view">
        <field name="name">patrimoine.mouvement.search</field>
        <field name="model">patrimoine.mouvement</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="bien_id"/>
                <field name="detenteur_origine_id"/>
                <field name="detenteur_destination_id"/>
                <field name="motif"/>
                <separator/>
                <filter name="entree" string="Entrées" domain="[('type_mouvement', '=', 'entree')]"/>
                <filter name="sortie" string="Sorties" domain="[('type_mouvement', '=', 'sortie')]"/>
                <filter name="transfert" string="Transferts" domain="[('type_mouvement', '=', 'transfert')]"/>
                <filter name="reforme" string="Réformes" domain="[('type_mouvement', '=', 'reforme')]"/>
                <filter name="reparation" string="Réparations" domain="[('type_mouvement', '=', 'reparation')]"/>
                <separator/>
                <filter name="draft" string="Brouillon" domain="[('state', '=', 'draft')]"/>
                <filter name="confirmed" string="Confirmé" domain="[('state', '=', 'confirmed')]"/>
                <filter name="done" string="Effectué" domain="[('state', '=', 'done')]"/>
                <separator/>
                <filter name="today" string="Aujourd'hui" domain="[('date_mouvement', '=', context_today().strftime('%Y-%m-%d'))]"/>
                <filter name="this_week" string="Cette semaine" domain="[('date_mouvement', '&gt;=', (context_today() - datetime.timedelta(days=7)).strftime('%Y-%m-%d'))]"/>
                <filter name="this_month" string="Ce mois" domain="[('date_mouvement', '&gt;=', (context_today().replace(day=1)).strftime('%Y-%m-%d'))]"/>
                <separator/>
                <group expand="0" string="Grouper par">
                    <filter name="group_type" string="Type de Mouvement" context="{'group_by': 'type_mouvement'}"/>
                    <filter name="group_state" string="État" context="{'group_by': 'state'}"/>
                    <filter name="group_date" string="Date" context="{'group_by': 'date_mouvement'}"/>
                    <filter name="group_bien" string="Bien" context="{'group_by': 'bien_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action pour les mouvements -->
    <record id="patrimoine_mouvement_action" model="ir.actions.act_window">
        <field name="name">Mouvements</field>
        <field name="res_model">patrimoine.mouvement</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="patrimoine_mouvement_search_view"/>
        <field name="context">{'search_default_this_month': 1}</field>
    </record>
</odoo>



