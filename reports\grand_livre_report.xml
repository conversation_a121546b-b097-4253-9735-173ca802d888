<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Action pour le grand livre -->
    <record id="action_grand_livre_report" model="ir.actions.report">
        <field name="name">Grand Livre Comptabilité <PERSON>ière</field>
        <field name="model">patrimoine.bien</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">patrimoine_management.grand_livre_template</field>
        <field name="report_file">patrimoine_management.grand_livre_template</field>
        <field name="binding_model_id" ref="model_patrimoine_bien"/>
        <field name="binding_type">report</field>
    </record>

    <!-- Template du grand livre -->
    <template id="grand_livre_template">
        <t t-call="web.html_container">
            <div class="header">
                <div class="row">
                    <div class="col-6">
                        <img t-if="company.logo" t-att-src="image_data_uri(company.logo)" style="max-height: 45px;" alt="Logo"/>
                    </div>
                    <div class="col-6 text-right">
                        <h4 t-field="company.name"/>
                        <div t-field="company.partner_id" t-options='{"widget": "contact", "fields": ["address"], "no_marker": true}'/>
                    </div>
                </div>
            </div>

            <t t-foreach="docs" t-as="o">
                <div class="page">
                    <h2 class="text-center">Grand Livre Comptabilité Matière</h2>

                    <!-- Informations générales du bien -->
                    <div class="row mt-4">
                        <div class="col-6">
                            <table class="table table-sm">
                                <tr><td><strong>Désignation:</strong></td><td><span t-field="o.designation"/></td></tr>
                                <tr><td><strong>N° Inventaire:</strong></td><td><span t-field="o.numero_inventaire"/></td></tr>
                                <tr><td><strong>Marque:</strong></td><td><span t-field="o.marque"/></td></tr>
                                <tr><td><strong>Modèle:</strong></td><td><span t-field="o.modele"/></td></tr>
                                <tr><td><strong>N° Série:</strong></td><td><span t-field="o.numero_serie"/></td></tr>
                            </table>
                        </div>
                        <div class="col-6">
                            <table class="table table-sm">
                                <tr><td><strong>État:</strong></td><td><span t-field="o.etat" t-options='{"widget": "badge"}'/></td></tr>
                                <tr><td><strong>Valeur d'acquisition:</strong></td><td><span t-field="o.valeur_acquisition" t-options='{"widget": "monetary"}'/></td></tr>
                                <tr><td><strong>Valeur actuelle:</strong></td><td><span t-field="o.valeur_actuelle" t-options='{"widget": "monetary"}'/></td></tr>
                                <tr><td><strong>Quantité en stock:</strong></td><td><span t-field="o.quantite_stock"/></td></tr>
                                <tr><td><strong>Détenteur actuel:</strong></td><td><span t-field="o.detenteur_actuel_id.name"/></td></tr>
                            </table>
                        </div>
                    </div>

                    <!-- Dates importantes -->
                    <div class="row mt-3">
                        <div class="col-12">
                            <h5>Dates importantes</h5>
                            <table class="table table-sm">
                                <tr><td><strong>Date d'acquisition:</strong></td><td><span t-field="o.date_acquisition"/></td></tr>
                                <tr><td><strong>Date mise en service:</strong></td><td><span t-field="o.date_mise_service"/></td></tr>
                                <tr t-if="o.date_reforme"><td><strong>Date de réforme:</strong></td><td><span t-field="o.date_reforme"/></td></tr>
                                <tr t-if="o.date_cession"><td><strong>Date de cession:</strong></td><td><span t-field="o.date_cession"/></td></tr>
                                <tr t-if="o.date_perte"><td><strong>Date de perte:</strong></td><td><span t-field="o.date_perte"/></td></tr>
                            </table>
                        </div>
                    </div>

                    <!-- Historique des Mouvements -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <h5>Historique des Mouvements</h5>
                            <table class="table table-sm table-bordered">
                                <thead class="thead-light">
                                    <tr>
                                        <th>Date</th>
                                        <th>Référence</th>
                                        <th>Type</th>
                                        <th>Détenteur Origine</th>
                                        <th>Détenteur Destination</th>
                                        <th>Quantité</th>
                                        <th>Motif</th>
                                        <th>État</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <t t-foreach="o.mouvement_ids.sorted('date_mouvement')" t-as="mouvement">
                                        <tr>
                                            <td><span t-field="mouvement.date_mouvement"/></td>
                                            <td><span t-field="mouvement.name"/></td>
                                            <td><span t-field="mouvement.type_mouvement"/></td>
                                            <td><span t-field="mouvement.detenteur_origine_id.name"/></td>
                                            <td><span t-field="mouvement.detenteur_destination_id.name"/></td>
                                            <td class="text-right"><span t-field="mouvement.quantite"/></td>
                                            <td><span t-field="mouvement.motif"/></td>
                                            <td><span t-field="mouvement.state" t-options='{"widget": "badge"}'/></td>
                                        </tr>
                                    </t>
                                    <t t-if="not o.mouvement_ids">
                                        <tr>
                                            <td colspan="8" class="text-center text-muted">Aucun mouvement enregistré</td>
                                        </tr>
                                    </t>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Procès-verbaux -->
                    <div class="row mt-4" t-if="o.proces_verbal_ids">
                        <div class="col-12">
                            <h5>Procès-Verbaux</h5>
                            <table class="table table-sm table-bordered">
                                <thead class="thead-light">
                                    <tr>
                                        <th>Date</th>
                                        <th>Référence</th>
                                        <th>Type</th>
                                        <th>Président</th>
                                        <th>État</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <t t-foreach="o.proces_verbal_ids.sorted('date_proces_verbal')" t-as="pv">
                                        <tr>
                                            <td><span t-field="pv.date_proces_verbal"/></td>
                                            <td><span t-field="pv.name"/></td>
                                            <td><span t-field="pv.type_proces_verbal"/></td>
                                            <td><span t-field="pv.president_commission"/></td>
                                            <td><span t-field="pv.state" t-options='{"widget": "badge"}'/></td>
                                        </tr>
                                    </t>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Résumé -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <h6>Résumé</h6>
                                <p><strong>Total des mouvements:</strong> <span t-esc="len(o.mouvement_ids)"/></p>
                                <p><strong>Total des procès-verbaux:</strong> <span t-esc="len(o.proces_verbal_ids)"/></p>
                                <p><strong>Localisation actuelle:</strong> <span t-field="o.localisation"/></p>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </t>
    </template>
</odoo>



