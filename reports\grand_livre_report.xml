<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Action pour le grand livre -->
    <record id="action_grand_livre_report" model="ir.actions.report">
        <field name="name">Grand Livre Patrimonial</field>
        <field name="model">patrimoine.bien</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">patrimoine_management.grand_livre_template</field>
        <field name="report_file">patrimoine_management.grand_livre_template</field>
        <field name="binding_model_id" ref="model_patrimoine_bien"/>
        <field name="binding_type">report</field>
    </record>

    <!-- Template du grand livre -->
    <template id="grand_livre_template">
        <t t-call="web.html_container">
            <t t-foreach="docs" t-as="o">
                <div class="page">
                    <h2>Grand Livre Patrimonial</h2>
                    <h3><span t-field="o.designation"/></h3>
                    <p><strong>N° Inventaire:</strong> <span t-field="o.numero_inventaire"/></p>
                    <p><strong>État:</strong> <span t-field="o.etat"/></p>
                    <p><strong>Valeur:</strong> <span t-field="o.valeur_acquisition"/></p>
                    
                    <h4>Historique des Mouvements</h4>
                    <table class="table table-condensed">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Type</th>
                                <th>Motif</th>
                                <th>État</th>
                            </tr>
                        </thead>
                        <tbody>
                            <t t-foreach="o.mouvement_ids" t-as="mouvement">
                                <tr>
                                    <td><span t-field="mouvement.date_mouvement"/></td>
                                    <td><span t-field="mouvement.type_mouvement"/></td>
                                    <td><span t-field="mouvement.motif"/></td>
                                    <td><span t-field="mouvement.state"/></td>
                                </tr>
                            </t>
                        </tbody>
                    </table>
                </div>
            </t>
        </t>
    </template>
</odoo>



